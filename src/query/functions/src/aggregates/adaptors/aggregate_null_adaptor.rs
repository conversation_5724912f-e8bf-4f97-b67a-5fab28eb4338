// Copyright 2021 Datafuse Labs
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

use std::fmt;
use std::sync::Arc;

use databend_common_exception::Result;
use databend_common_expression::types::AnyType;
use databend_common_expression::types::Bitmap;
use databend_common_expression::types::DataType;
use databend_common_expression::types::NumberDataType;
use databend_common_expression::utils::column_merge_validity;
use databend_common_expression::BlockEntry;
use databend_common_expression::Column;
use databend_common_expression::ColumnBuilder;
use databend_common_expression::ProjectedBlock;
use databend_common_expression::Scalar;
use databend_common_expression::ScalarRef;
use databend_common_expression::StateSerdeItem;

use super::AggrState;
use super::AggrStateLoc;
use super::AggrStateRegistry;
use super::AggrStateType;
use super::AggregateFunction;
use super::AggregateFunctionFeatures;
use super::AggregateFunctionRef;
use super::AggregateNullResultFunction;
use super::StateAddr;

#[derive(Clone)]
pub struct AggregateFunctionCombinatorNull {}

impl AggregateFunctionCombinatorNull {
    pub fn transform_arguments(arguments: &[DataType]) -> Result<Vec<DataType>> {
        let mut results = Vec::with_capacity(arguments.len());

        for arg in arguments.iter() {
            match arg {
                DataType::Nullable(box ty) => {
                    results.push(ty.clone());
                }
                _ => {
                    results.push(arg.clone());
                }
            }
        }
        Ok(results)
    }

    pub fn transform_params(params: &[Scalar]) -> Result<Vec<Scalar>> {
        Ok(params.to_owned())
    }

    pub fn try_create(
        _name: &str,
        params: Vec<Scalar>,
        arguments: Vec<DataType>,
        nested: AggregateFunctionRef,
        properties: AggregateFunctionFeatures,
    ) -> Result<AggregateFunctionRef> {
        // has_null_types
        if arguments.iter().any(|f| f == &DataType::Null) {
            if properties.returns_default_when_only_null {
                return AggregateNullResultFunction::try_create(DataType::Number(
                    NumberDataType::UInt64,
                ));
            } else {
                return AggregateNullResultFunction::try_create(DataType::Null);
            }
        }
        let params = Self::transform_params(&params)?;
        let arguments = Self::transform_arguments(&arguments)?;
        let size = arguments.len();

        // Some functions may have their own null adaptor
        if let Some(null_adaptor) =
            nested.get_own_null_adaptor(nested.clone(), params, arguments)?
        {
            return Ok(null_adaptor);
        }

        let return_type = nested.return_type()?;
        let result_is_null =
            !properties.returns_default_when_only_null && return_type.can_inside_nullable();

        match size {
            1 => match result_is_null {
                true => Ok(AggregateNullUnaryAdaptor::<true>::create(nested)),
                false => Ok(AggregateNullUnaryAdaptor::<false>::create(nested)),
            },

            _ => match result_is_null {
                true => Ok(AggregateNullVariadicAdaptor::<true>::create(nested)),
                false => Ok(AggregateNullVariadicAdaptor::<false>::create(nested)),
            },
        }
    }
}

#[derive(Clone)]
pub struct AggregateNullUnaryAdaptor<const NULLABLE_RESULT: bool>(
    CommonNullAdaptor<NULLABLE_RESULT>,
);

impl<const NULLABLE_RESULT: bool> AggregateNullUnaryAdaptor<NULLABLE_RESULT> {
    pub fn create(nested: AggregateFunctionRef) -> AggregateFunctionRef {
        Arc::new(Self(CommonNullAdaptor::<NULLABLE_RESULT> { nested }))
    }
}

impl<const NULLABLE_RESULT: bool> AggregateFunction for AggregateNullUnaryAdaptor<NULLABLE_RESULT> {
    fn name(&self) -> &str {
        "AggregateNullUnaryAdaptor"
    }

    fn return_type(&self) -> Result<DataType> {
        self.0.return_type()
    }

    #[inline]
    fn init_state(&self, place: AggrState) {
        self.0.init_state(place);
    }

    fn register_state(&self, registry: &mut AggrStateRegistry) {
        self.0.register_state(registry);
    }

    #[inline]
    fn accumulate(
        &self,
        place: AggrState,
        columns: ProjectedBlock,
        validity: Option<&Bitmap>,
        input_rows: usize,
    ) -> Result<()> {
        let entry = &columns[0];
        let validity = column_merge_validity(entry, validity.cloned());
        let not_null_column = &[entry.clone().remove_nullable()];
        let not_null_column = not_null_column.into();
        let validity = Bitmap::map_all_sets_to_none(validity);

        self.0
            .accumulate(place, not_null_column, validity, input_rows)
    }

    #[inline]
    fn accumulate_keys(
        &self,
        addrs: &[StateAddr],
        loc: &[AggrStateLoc],
        columns: ProjectedBlock,
        input_rows: usize,
    ) -> Result<()> {
        let entry = &columns[0];
        let validity = column_merge_validity(entry, None);
        let not_null_columns = &[entry.clone().remove_nullable()];
        let not_null_columns = not_null_columns.into();

        self.0
            .accumulate_keys(addrs, loc, not_null_columns, validity, input_rows)
    }

    fn accumulate_row(&self, place: AggrState, columns: ProjectedBlock, row: usize) -> Result<()> {
        let entry = &columns[0];
        let validity = column_merge_validity(entry, None);
        let not_null_columns = &[entry.clone().remove_nullable()];
        let not_null_columns = not_null_columns.into();

        self.0
            .accumulate_row(place, not_null_columns, validity, row)
    }

    fn serialize_type(&self) -> Vec<StateSerdeItem> {
        self.0.serialize_type()
    }

    fn serialize(&self, place: AggrState, builders: &mut [ColumnBuilder]) -> Result<()> {
        self.0.serialize(place, builders)
    }

    fn merge(&self, place: AggrState, data: &[ScalarRef]) -> Result<()> {
        self.0.merge(place, data)
    }

    fn batch_merge(
        &self,
        places: &[StateAddr],
        loc: &[AggrStateLoc],
        state: &BlockEntry,
        filter: Option<&Bitmap>,
    ) -> Result<()> {
        self.0.batch_merge(places, loc, state, filter)
    }

    fn merge_states(&self, place: AggrState, rhs: AggrState) -> Result<()> {
        self.0.merge_states(place, rhs)
    }

    fn merge_result(&self, place: AggrState, builder: &mut ColumnBuilder) -> Result<()> {
        self.0.merge_result(place, builder)
    }

    fn need_manual_drop_state(&self) -> bool {
        self.0.nested.need_manual_drop_state()
    }

    unsafe fn drop_state(&self, place: AggrState) {
        self.0.drop_state(place);
    }

    fn get_if_condition(&self, columns: ProjectedBlock) -> Option<Bitmap> {
        self.0.nested.get_if_condition(columns)
    }
}

impl<const NULLABLE_RESULT: bool> fmt::Display for AggregateNullUnaryAdaptor<NULLABLE_RESULT> {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "AggregateNullUnaryAdaptor")
    }
}

#[derive(Clone)]
pub struct AggregateNullVariadicAdaptor<const NULLABLE_RESULT: bool>(
    CommonNullAdaptor<NULLABLE_RESULT>,
);

impl<const NULLABLE_RESULT: bool> AggregateNullVariadicAdaptor<NULLABLE_RESULT> {
    pub fn create(nested: AggregateFunctionRef) -> AggregateFunctionRef {
        Arc::new(Self(CommonNullAdaptor::<NULLABLE_RESULT> { nested }))
    }
}

impl<const NULLABLE_RESULT: bool> AggregateFunction
    for AggregateNullVariadicAdaptor<NULLABLE_RESULT>
{
    fn name(&self) -> &str {
        "AggregateNullVariadicAdaptor"
    }

    fn return_type(&self) -> Result<DataType> {
        self.0.return_type()
    }

    fn init_state(&self, place: AggrState) {
        self.0.init_state(place);
    }

    fn register_state(&self, registry: &mut AggrStateRegistry) {
        self.0.register_state(registry);
    }

    #[inline]
    fn accumulate(
        &self,
        place: AggrState,
        columns: ProjectedBlock,
        validity: Option<&Bitmap>,
        input_rows: usize,
    ) -> Result<()> {
        let mut not_null_columns = Vec::with_capacity(columns.len());
        let mut validity = validity.cloned();
        for entry in columns.iter() {
            validity = column_merge_validity(&entry.clone(), validity);
            not_null_columns.push(entry.clone().remove_nullable());
        }
        let not_null_columns = (&not_null_columns).into();

        self.0
            .accumulate(place, not_null_columns, validity, input_rows)
    }

    fn accumulate_keys(
        &self,
        addrs: &[StateAddr],
        loc: &[AggrStateLoc],
        columns: ProjectedBlock,
        input_rows: usize,
    ) -> Result<()> {
        let mut not_null_columns = Vec::with_capacity(columns.len());
        let mut validity = None;
        for entry in columns.iter() {
            validity = column_merge_validity(&entry.clone(), validity);
            not_null_columns.push(entry.clone().remove_nullable());
        }
        let not_null_columns = (&not_null_columns).into();

        self.0
            .accumulate_keys(addrs, loc, not_null_columns, validity, input_rows)
    }

    fn accumulate_row(&self, place: AggrState, columns: ProjectedBlock, row: usize) -> Result<()> {
        let mut not_null_columns = Vec::with_capacity(columns.len());
        let mut validity = None;
        for entry in columns.iter() {
            validity = column_merge_validity(&entry.clone(), validity);
            not_null_columns.push(entry.clone().remove_nullable());
        }
        let not_null_columns = (&not_null_columns).into();

        self.0
            .accumulate_row(place, not_null_columns, validity, row)
    }

    fn serialize_type(&self) -> Vec<StateSerdeItem> {
        self.0.serialize_type()
    }

    fn serialize(&self, place: AggrState, builders: &mut [ColumnBuilder]) -> Result<()> {
        self.0.serialize(place, builders)
    }

    fn merge(&self, place: AggrState, data: &[ScalarRef]) -> Result<()> {
        self.0.merge(place, data)
    }

    fn batch_merge(
        &self,
        places: &[StateAddr],
        loc: &[AggrStateLoc],
        state: &BlockEntry,
        filter: Option<&Bitmap>,
    ) -> Result<()> {
        self.0.batch_merge(places, loc, state, filter)
    }

    fn merge_states(&self, place: AggrState, rhs: AggrState) -> Result<()> {
        self.0.merge_states(place, rhs)
    }

    fn merge_result(&self, place: AggrState, builder: &mut ColumnBuilder) -> Result<()> {
        self.0.merge_result(place, builder)
    }

    fn need_manual_drop_state(&self) -> bool {
        self.0.nested.need_manual_drop_state()
    }

    unsafe fn drop_state(&self, place: AggrState) {
        self.0.drop_state(place);
    }

    fn get_if_condition(&self, columns: ProjectedBlock) -> Option<Bitmap> {
        self.0.nested.get_if_condition(columns)
    }
}

impl<const NULLABLE_RESULT: bool> fmt::Display for AggregateNullVariadicAdaptor<NULLABLE_RESULT> {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "AggregateNullVariadicAdaptor")
    }
}

#[derive(Clone)]
struct CommonNullAdaptor<const NULLABLE_RESULT: bool> {
    nested: AggregateFunctionRef,
}

impl<const NULLABLE_RESULT: bool> CommonNullAdaptor<NULLABLE_RESULT> {
    fn return_type(&self) -> Result<DataType> {
        if !NULLABLE_RESULT {
            return self.nested.return_type();
        }

        let nested = self.nested.return_type()?;
        Ok(nested.wrap_nullable())
    }

    fn init_state(&self, place: AggrState) {
        if !NULLABLE_RESULT {
            return self.nested.init_state(place);
        }

        set_flag(place, false);
        self.nested.init_state(place.remove_last_loc());
    }

    fn register_state(&self, registry: &mut AggrStateRegistry) {
        self.nested.register_state(registry);
        if NULLABLE_RESULT {
            registry.register(AggrStateType::Bool);
        }
    }

    #[inline]
    fn accumulate(
        &self,
        place: AggrState,
        not_null_column: ProjectedBlock,
        validity: Option<Bitmap>,
        input_rows: usize,
    ) -> Result<()> {
        if !NULLABLE_RESULT {
            return self
                .nested
                .accumulate(place, not_null_column, validity.as_ref(), input_rows);
        }

        if validity
            .as_ref()
            .map(|c| c.null_count() != input_rows)
            .unwrap_or(true)
        {
            set_flag(place, true);
        }
        self.nested.accumulate(
            place.remove_last_loc(),
            not_null_column,
            validity.as_ref(),
            input_rows,
        )
    }

    fn accumulate_keys(
        &self,
        addrs: &[StateAddr],
        loc: &[AggrStateLoc],
        not_null_columns: ProjectedBlock,
        validity: Option<Bitmap>,
        input_rows: usize,
    ) -> Result<()> {
        match validity {
            Some(v) if v.null_count() > 0 => {
                // all nulls
                if v.null_count() == v.len() {
                    return Ok(());
                }

                for (valid, (row, place)) in v.iter().zip(addrs.iter().enumerate()) {
                    if !valid {
                        continue;
                    }
                    let place = AggrState::new(*place, loc);
                    if NULLABLE_RESULT {
                        set_flag(place, true);
                        self.nested.accumulate_row(
                            place.remove_last_loc(),
                            not_null_columns,
                            row,
                        )?;
                    } else {
                        self.nested.accumulate_row(place, not_null_columns, row)?;
                    };
                }
                Ok(())
            }
            _ => {
                if !NULLABLE_RESULT {
                    self.nested
                        .accumulate_keys(addrs, loc, not_null_columns, input_rows)
                } else {
                    addrs
                        .iter()
                        .for_each(|addr| set_flag(AggrState::new(*addr, loc), true));
                    self.nested.accumulate_keys(
                        addrs,
                        &loc[..loc.len() - 1],
                        not_null_columns,
                        input_rows,
                    )
                }
            }
        }
    }

    fn accumulate_row(
        &self,
        place: AggrState,
        not_null_columns: ProjectedBlock,
        validity: Option<Bitmap>,
        row: usize,
    ) -> Result<()> {
        let v = if let Some(v) = validity {
            if v.null_count() == 0 {
                true
            } else if v.null_count() == v.len() {
                false
            } else {
                unsafe { v.get_bit_unchecked(row) }
            }
        } else {
            true
        };
        if !v {
            return Ok(());
        }

        if !NULLABLE_RESULT {
            return self.nested.accumulate_row(place, not_null_columns, row);
        }

        set_flag(place, true);
        self.nested
            .accumulate_row(place.remove_last_loc(), not_null_columns, row)
    }

    fn serialize_type(&self) -> Vec<StateSerdeItem> {
        if !NULLABLE_RESULT {
            return self.nested.serialize_type();
        }
        self.nested
            .serialize_type()
            .into_iter()
            .chain(Some(StateSerdeItem::DataType(DataType::Boolean)))
            .collect()
    }

    fn serialize(&self, place: AggrState, builders: &mut [ColumnBuilder]) -> Result<()> {
        if !NULLABLE_RESULT {
            return self.nested.serialize(place, builders);
        }
        let n = builders.len();
        debug_assert_eq!(self.nested.serialize_type().len() + 1, n);

        let flag = get_flag(place);
        builders
            .last_mut()
            .and_then(ColumnBuilder::as_boolean_mut)
            .unwrap()
            .push(flag);
        self.nested
            .serialize(place.remove_last_loc(), &mut builders[..(n - 1)])
    }

    fn merge(&self, place: AggrState, data: &[ScalarRef]) -> Result<()> {
        if !NULLABLE_RESULT {
            return self.nested.merge(place, data);
        }

        let flag = *data.last().and_then(ScalarRef::as_boolean).unwrap();
        if !flag {
            return Ok(());
        }

        self.update_flag(place);
        self.nested
            .merge(place.remove_last_loc(), &data[..data.len() - 1])
    }

    fn batch_merge(
        &self,
        places: &[StateAddr],
        loc: &[AggrStateLoc],
        state: &BlockEntry,
        filter: Option<&Bitmap>,
    ) -> Result<()> {
        if !NULLABLE_RESULT {
            return self.nested.batch_merge(places, loc, state, filter);
        }

        match state {
            BlockEntry::Column(Column::Tuple(tuple)) => {
                let nested_state = Column::Tuple(tuple[0..tuple.len() - 1].to_vec());
                let flag = tuple.last().unwrap().as_boolean().unwrap();
                let flag = match filter {
                    Some(filter) => filter & flag,
                    None => flag.clone(),
                };
                let filter = if flag.null_count() == 0 {
                    for place in places.iter() {
                        self.update_flag(AggrState::new(*place, loc));
                    }
                    None
                } else {
                    for place in places
                        .iter()
                        .zip(flag.iter())
                        .filter_map(|(place, flag)| flag.then_some(place))
                    {
                        self.update_flag(AggrState::new(*place, loc));
                    }
                    Some(&flag)
                };
                self.nested
                    .batch_merge(places, &loc[..loc.len() - 1], &nested_state.into(), filter)
            }
            _ => {
                let state = state.downcast::<AnyType>().unwrap();
                for (place, data) in places.iter().zip(state.iter()) {
                    self.merge(
                        AggrState::new(*place, loc),
                        data.as_tuple().unwrap().as_slice(),
                    )?;
                }
                Ok(())
            }
        }
    }

    fn merge_states(&self, place: AggrState, rhs: AggrState) -> Result<()> {
        if !NULLABLE_RESULT {
            return self.nested.merge_states(place, rhs);
        }

        if !get_flag(rhs) {
            return Ok(());
        }

        if !get_flag(place) {
            // initial the state to remove the dirty stats
            self.init_state(place);
        }
        set_flag(place, true);
        self.nested
            .merge_states(place.remove_last_loc(), rhs.remove_last_loc())
    }

    fn merge_result(&self, place: AggrState, builder: &mut ColumnBuilder) -> Result<()> {
        if !NULLABLE_RESULT {
            return self.nested.merge_result(place, builder);
        }

        let ColumnBuilder::Nullable(ref mut inner) = builder else {
            unreachable!()
        };

        if get_flag(place) {
            inner.validity.push(true);
            self.nested
                .merge_result(place.remove_last_loc(), &mut inner.builder)
        } else {
            inner.push_null();
            Ok(())
        }
    }

    unsafe fn drop_state(&self, place: AggrState) {
        if !NULLABLE_RESULT {
            self.nested.drop_state(place)
        } else {
            self.nested.drop_state(place.remove_last_loc())
        }
    }

    fn update_flag(&self, place: AggrState) {
        if !get_flag(place) {
            // initial the state to remove the dirty stats
            self.init_state(place);
        }
        set_flag(place, true);
    }
}

fn set_flag(place: AggrState, flag: bool) {
    let c = place.addr.next(flag_offset(place)).get::<u8>();
    *c = flag as u8;
}

fn get_flag(place: AggrState) -> bool {
    let c = place.addr.next(flag_offset(place)).get::<u8>();
    *c != 0
}

fn flag_offset(place: AggrState) -> usize {
    *place.loc.last().unwrap().as_bool().unwrap().1
}

#[cfg(test)]
mod tests {

    //! Tests to prove that the optimized batch_merge implementation is equivalent to the default implementation.
    //!
    //! The CommonNullAdaptor has two batch_merge implementations:
    //! 1. **Default implementation** (in AggregateFunction trait): Calls merge() for each element individually
    //! 2. **Optimized implementation** (in CommonNullAdaptor): Directly processes tuple structure and batch updates flags
    //!
    //! These tests verify that both implementations process the same data and produce equivalent results.

    use std::sync::Arc;

    use databend_common_expression::types::*;
    use databend_common_expression::*;

    use super::*;

    /// Configuration for batch merge implementation strategy
    #[derive(Debug, Clone, Copy, PartialEq, Eq)]
    pub enum BatchMergeStrategy {
        /// Use the reference implementation (default batch_merge logic)
        Reference,
        /// Use the optimized implementation
        Optimized,
    }

    /// A configurable aggregate function that wraps CommonNullAdaptor and allows switching
    /// between reference and optimized batch_merge implementations for testing purposes.
    pub struct ConfigurableNullAggregateFunction<const NULLABLE_RESULT: bool> {
        adaptor: CommonNullAdaptor<NULLABLE_RESULT>,
        strategy: BatchMergeStrategy,
    }

    impl<const NULLABLE_RESULT: bool> ConfigurableNullAggregateFunction<NULLABLE_RESULT> {
        pub fn new(nested: AggregateFunctionRef, strategy: BatchMergeStrategy) -> Self {
            Self {
                adaptor: CommonNullAdaptor { nested },
                strategy,
            }
        }

        /// Reference implementation of batch_merge extracted from the default trait implementation
        fn reference_batch_merge_impl(
            &self,
            places: &[StateAddr],
            loc: &[AggrStateLoc],
            state: &BlockEntry,
            filter: Option<&Bitmap>,
        ) -> Result<()> {
            // This is the exact logic from the default batch_merge implementation in AggregateFunction trait
            let view = state.downcast::<AnyType>().unwrap();
            let iter = places.iter().zip(view.iter());

            if let Some(filter) = filter {
                for (i, (place, data)) in iter.enumerate() {
                    if unsafe { filter.get_bit_unchecked(i) } {
                        // For nullable results, also check the flag in the tuple
                        let should_process = if NULLABLE_RESULT {
                            let tuple_data = data.as_tuple().unwrap().as_slice();
                            if let ScalarRef::Boolean(flag) = tuple_data[1] {
                                flag
                            } else {
                                false
                            }
                        } else {
                            true
                        };

                        if should_process {
                            let place = AggrState::new(*place, loc);
                            self.merge(
                                place.remove_last_loc(),
                                data.as_tuple().unwrap().as_slice(),
                            )?;
                        }
                    }
                }
            } else {
                for (place, data) in iter {
                    // For nullable results, check the flag in the tuple
                    let should_process = if NULLABLE_RESULT {
                        let tuple_data = data.as_tuple().unwrap().as_slice();
                        if let ScalarRef::Boolean(flag) = tuple_data[1] {
                            flag
                        } else {
                            false
                        }
                    } else {
                        true
                    };

                    if should_process {
                        let place = AggrState::new(*place, loc);
                        self.merge(place.remove_last_loc(), data.as_tuple().unwrap().as_slice())?;
                    }
                }
            }

            Ok(())
        }
    }

    impl<const NULLABLE_RESULT: bool> fmt::Display
        for ConfigurableNullAggregateFunction<NULLABLE_RESULT>
    {
        fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
            write!(
                f,
                "ConfigurableNull[{}]({}, strategy: {:?})",
                if NULLABLE_RESULT {
                    "nullable"
                } else {
                    "non-nullable"
                },
                self.adaptor.nested,
                self.strategy
            )
        }
    }

    impl<const NULLABLE_RESULT: bool> AggregateFunction
        for ConfigurableNullAggregateFunction<NULLABLE_RESULT>
    {
        fn name(&self) -> &str {
            "configurable_null"
        }

        fn return_type(&self) -> Result<DataType> {
            self.adaptor.return_type()
        }

        fn init_state(&self, place: AggrState) {
            self.adaptor.init_state(place)
        }

        fn register_state(&self, registry: &mut AggrStateRegistry) {
            self.adaptor.register_state(registry)
        }

        fn accumulate(
            &self,
            place: AggrState,
            columns: ProjectedBlock,
            validity: Option<&Bitmap>,
            input_rows: usize,
        ) -> Result<()> {
            self.adaptor
                .accumulate(place, columns, validity.cloned(), input_rows)
        }

        fn accumulate_row(
            &self,
            place: AggrState,
            columns: ProjectedBlock,
            row: usize,
        ) -> Result<()> {
            self.adaptor.accumulate_row(place, columns, None, row)
        }

        fn serialize_type(&self) -> Vec<StateSerdeItem> {
            self.adaptor.serialize_type()
        }

        fn serialize(&self, place: AggrState, builders: &mut [ColumnBuilder]) -> Result<()> {
            self.adaptor.serialize(place, builders)
        }

        fn merge(&self, place: AggrState, data: &[ScalarRef]) -> Result<()> {
            self.adaptor.merge(place, data)
        }

        fn merge_states(&self, place: AggrState, rhs: AggrState) -> Result<()> {
            self.adaptor.merge_states(place, rhs)
        }

        fn merge_result(&self, place: AggrState, builder: &mut ColumnBuilder) -> Result<()> {
            self.adaptor.merge_result(place, builder)
        }

        /// Custom batch_merge implementation that switches between reference and optimized strategies
        fn batch_merge(
            &self,
            places: &[StateAddr],
            loc: &[AggrStateLoc],
            state: &BlockEntry,
            filter: Option<&Bitmap>,
        ) -> Result<()> {
            match self.strategy {
                BatchMergeStrategy::Reference => {
                    self.reference_batch_merge_impl(places, loc, state, filter)
                }
                BatchMergeStrategy::Optimized => {
                    self.adaptor.batch_merge(places, loc, state, filter)
                }
            }
        }
    }

    /// Test helper structure that manages a buffer and its corresponding StateAddr
    struct TestStateBuffer {
        buffer: Vec<u8>,
        #[allow(dead_code)]
        addr: StateAddr,
    }

    impl TestStateBuffer {
        fn new(layout: &std::alloc::Layout) -> Self {
            let mut buffer = vec![0u8; layout.size()];
            let addr = StateAddr::new(buffer.as_mut_ptr() as usize);
            Self { buffer, addr }
        }

        fn addr(&self) -> StateAddr {
            // Return a StateAddr pointing to our buffer
            StateAddr::new(self.buffer.as_ptr() as usize)
        }
    }

    // Mock aggregate function for testing
    #[derive(Clone)]
    struct MockAggregateFunction {
        return_type: DataType,
        serialize_items: Vec<StateSerdeItem>,
    }

    impl fmt::Display for MockAggregateFunction {
        fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
            write!(f, "MockAggregateFunction")
        }
    }

    impl AggregateFunction for MockAggregateFunction {
        fn name(&self) -> &str {
            "mock"
        }

        fn return_type(&self) -> Result<DataType> {
            Ok(self.return_type.clone())
        }

        fn init_state(&self, _place: AggrState) {}

        fn register_state(&self, registry: &mut AggrStateRegistry) {
            registry.register(AggrStateType::Custom(std::alloc::Layout::new::<u64>()));
        }

        fn accumulate(
            &self,
            _place: AggrState,
            _columns: ProjectedBlock,
            _validity: Option<&Bitmap>,
            _input_rows: usize,
        ) -> Result<()> {
            Ok(())
        }

        fn accumulate_row(
            &self,
            _place: AggrState,
            _columns: ProjectedBlock,
            _row: usize,
        ) -> Result<()> {
            Ok(())
        }

        fn serialize_type(&self) -> Vec<StateSerdeItem> {
            self.serialize_items.clone()
        }

        fn serialize(&self, place: AggrState, builders: &mut [ColumnBuilder]) -> Result<()> {
            let state = place.get::<u64>();
            if let ColumnBuilder::Number(NumberColumnBuilder::UInt64(builder)) = &mut builders[0] {
                builder.push(*state);
            }
            Ok(())
        }

        fn merge(&self, place: AggrState, data: &[ScalarRef]) -> Result<()> {
            let state = place.get::<u64>();
            if let Some(ScalarRef::Number(NumberScalar::UInt64(value))) = data.get(0) {
                *state += value;
            }
            Ok(())
        }

        fn merge_states(&self, place: AggrState, rhs: AggrState) -> Result<()> {
            let lhs_state = place.get::<u64>();
            let rhs_state = rhs.get::<u64>();
            *lhs_state += *rhs_state;
            Ok(())
        }

        fn merge_result(&self, place: AggrState, builder: &mut ColumnBuilder) -> Result<()> {
            let state = place.get::<u64>();
            if let ColumnBuilder::Number(NumberColumnBuilder::UInt64(builder)) = builder {
                builder.push(*state);
            }
            Ok(())
        }
    }

    fn create_mock_adaptor() -> CommonNullAdaptor<true> {
        let nested = Arc::new(MockAggregateFunction {
            return_type: DataType::Number(NumberDataType::UInt64),
            serialize_items: vec![StateSerdeItem::DataType(DataType::Number(
                NumberDataType::UInt64,
            ))],
        });
        CommonNullAdaptor::<true> { nested }
    }

    fn create_configurable(
        strategy: BatchMergeStrategy,
    ) -> ConfigurableNullAggregateFunction<true> {
        let nested = Arc::new(MockAggregateFunction {
            return_type: DataType::Number(NumberDataType::UInt64),
            serialize_items: vec![StateSerdeItem::DataType(DataType::Number(
                NumberDataType::UInt64,
            ))],
        });
        ConfigurableNullAggregateFunction::new(nested, strategy)
    }

    fn create_test_state_entry(values: &[u64], flags: &[bool]) -> Result<BlockEntry> {
        // Build the state column as a tuple
        let mut value_builder =
            ColumnBuilder::with_capacity(&DataType::Number(NumberDataType::UInt64), values.len());
        let mut flag_builder = ColumnBuilder::with_capacity(&DataType::Boolean, flags.len());

        for &value in values {
            if let ColumnBuilder::Number(NumberColumnBuilder::UInt64(builder)) = &mut value_builder
            {
                builder.push(value);
            }
        }

        for &flag in flags {
            if let ColumnBuilder::Boolean(builder) = &mut flag_builder {
                builder.push(flag);
            }
        }

        let value_column = value_builder.build();
        let flag_column = flag_builder.build();
        let tuple_column = Column::Tuple(vec![value_column, flag_column]);

        Ok(BlockEntry::new(Value::Column(tuple_column), || {
            (
                DataType::Tuple(vec![
                    DataType::Number(NumberDataType::UInt64),
                    DataType::Boolean,
                ]),
                values.len(),
            )
        }))
    }



    /// Helper function to run reference implementation using ConfigurableNullAggregateFunction
    fn run_reference_batch_merge<const NULLABLE_RESULT: bool>(
        test_buffers: &mut [TestStateBuffer],
        loc: &[AggrStateLoc],
        state: &BlockEntry,
        filter: Option<&Bitmap>,
    ) -> Result<()> {
        let reference_func = create_configurable_reference_function();
        let places: Vec<StateAddr> = test_buffers.iter().map(|b| b.addr()).collect();

        // Initialize states
        for buffer in test_buffers.iter() {
            let place = AggrState::new(buffer.addr(), loc);
            reference_func.init_state(place);
        }

        // Use the reference implementation
        reference_func.reference_batch_merge_impl(&places, loc, state, filter)
    }

    /// Extract the processing logic from the optimized batch_merge implementation.
    /// This manually implements the optimized logic without calling the actual batch_merge method.
    fn optimized_batch_merge_logic<const NULLABLE_RESULT: bool>(
        adaptor: &CommonNullAdaptor<NULLABLE_RESULT>,
        test_buffers: &mut [TestStateBuffer],
        loc: &[AggrStateLoc],
        _layout: &std::alloc::Layout,
        state: &BlockEntry,
        filter: Option<&Bitmap>,
    ) -> Result<()> {
        if !NULLABLE_RESULT {
            // For non-nullable results, fall back to reference implementation
            return run_reference_batch_merge::<NULLABLE_RESULT>(test_buffers, loc, state, filter);
        }

        // This manually implements the optimized logic from CommonNullAdaptor::batch_merge
        match state {
            BlockEntry::Column(Column::Tuple(tuple)) => {
                // Extract nested state and flag (this is the optimized approach)
                let nested_state = Column::Tuple(tuple[0..tuple.len() - 1].to_vec());
                let flag = tuple.last().unwrap().as_boolean().unwrap();
                let effective_flag = match filter {
                    Some(filter) => filter & flag,
                    None => flag.clone(),
                };

                // Initialize all states first
                for test_buffer in test_buffers.iter() {
                    let place = AggrState::new(test_buffer.addr(), loc);
                    adaptor.init_state(place);
                }

                // Manually implement the optimized batch processing logic
                // 1. Create nested state entry for batch processing
                let nested_entry = BlockEntry::new(Value::Column(nested_state), || {
                    (tuple[0].data_type(), test_buffers.len())
                });

                // 2. Get nested locations (exclude the flag location)
                let nested_loc = &loc[..loc.len() - 1];

                // 3. Batch process the nested states using the nested function
                let places: Vec<StateAddr> = test_buffers.iter().map(|b| b.addr()).collect();
                adaptor.nested.batch_merge(
                    &places,
                    nested_loc,
                    &nested_entry,
                    Some(&effective_flag),
                )?;

                // 4. Update flags for all processed items (this is the optimized part)
                for (i, test_buffer) in test_buffers.iter().enumerate() {
                    if unsafe { effective_flag.get_bit_unchecked(i) } {
                        let place = AggrState::new(test_buffer.addr(), loc);
                        set_flag(place, true);
                    }
                }
            }
            _ => {
                // Fall back to reference implementation for non-tuple states
                return reference_batch_merge(adaptor, test_buffers, loc, layout, state, filter);
            }
        }

        Ok(())
    }

    /// Test helper to verify the optimized flag processing logic
    fn test_optimized_flag_processing(
        test_buffers: &[TestStateBuffer],
        _loc: &[AggrStateLoc],
        flags: &[bool],
        filter: Option<&Bitmap>,
    ) -> Vec<bool> {
        let mut result_flags = vec![false; test_buffers.len()];

        // Simulate the optimized flag processing
        let flag_bitmap = Bitmap::from_iter(flags.iter().copied());
        let effective_flag = match filter {
            Some(filter) => filter & &flag_bitmap,
            None => flag_bitmap,
        };

        // Check which items would be processed
        for (i, _test_buffer) in test_buffers.iter().enumerate() {
            if unsafe { effective_flag.get_bit_unchecked(i) } {
                result_flags[i] = true;
            }
        }

        result_flags
    }

    /// Test helper to verify the reference flag processing logic
    fn test_reference_flag_processing(
        test_buffers: &[TestStateBuffer],
        _loc: &[AggrStateLoc],
        flags: &[bool],
        filter: Option<&Bitmap>,
    ) -> Vec<bool> {
        let mut result_flags = vec![false; test_buffers.len()];

        // Simulate the reference flag processing (individual checks)
        for (i, _test_buffer) in test_buffers.iter().enumerate() {
            let should_process_filter = match filter {
                Some(filter) => unsafe { filter.get_bit_unchecked(i) },
                None => true,
            };

            let should_process_flag = flags[i];

            if should_process_filter && should_process_flag {
                result_flags[i] = true;
            }
        }

        result_flags
    }

    /// Test that proves the optimized batch_merge implementation processes the same data
    /// as the default implementation would process.
    ///
    /// This test directly compares the processing logic of both implementations by:
    /// 1. Running the reference implementation (extracted from default batch_merge)
    /// 2. Running the optimized implementation logic
    /// 3. Verifying both produce identical results
    #[test]
    fn test_batch_merge_equivalence() -> Result<()> {
        let adaptor = create_mock_adaptor();

        // Create test state data - tuple with (u64_value, bool_flag)
        let values = vec![10u64, 20u64, 30u64];
        let flags = vec![true, false, true];

        let state_entry = create_test_state_entry(&values, &flags)?;

        // Setup state layout - create a manual layout that matches the adaptor structure
        // The adaptor has: nested state (u64) + bool flag
        let nested_layout = std::alloc::Layout::new::<u64>();
        let bool_layout = std::alloc::Layout::new::<bool>();
        let (layout, nested_offset) = nested_layout.extend(bool_layout).unwrap();
        let layout = layout.pad_to_align();

        // Create location descriptors
        let loc = vec![
            AggrStateLoc::Custom(0, 0),           // nested state at offset 0
            AggrStateLoc::Bool(1, nested_offset), // bool flag at calculated offset
        ];

        // Create test buffers - we need separate copies for each implementation
        let mut reference_buffers: Vec<TestStateBuffer> = (0..values.len())
            .map(|_| TestStateBuffer::new(&layout))
            .collect();
        let mut optimized_buffers: Vec<TestStateBuffer> = (0..values.len())
            .map(|_| TestStateBuffer::new(&layout))
            .collect();

        // Test 1: Run reference implementation (baseline)
        reference_batch_merge(
            &adaptor,
            &mut reference_buffers,
            &loc,
            &layout,
            &state_entry,
            None,
        )?;

        // Test 2: Run optimized implementation logic
        optimized_batch_merge_logic(
            &adaptor,
            &mut optimized_buffers,
            &loc,
            &layout,
            &state_entry,
            None,
        )?;

        // Test 3: Compare buffer contents
        // Only compare buffers that should have been processed (where flag=true)
        let processed_indices: Vec<usize> = flags
            .iter()
            .enumerate()
            .filter_map(|(i, &flag)| if flag { Some(i) } else { None })
            .collect();

        for &i in &processed_indices {
            // Compare the actual buffer contents for processed items
            assert_eq!(
                reference_buffers[i].buffer, optimized_buffers[i].buffer,
                "Buffer contents mismatch at index {}",
                i
            );
        }

        // Test 4: Verify only items with true flags were processed
        let expected_processed_count = flags.iter().filter(|&&f| f).count();
        assert_eq!(
            processed_indices.len(),
            expected_processed_count,
            "Should only process items where flag=true"
        );

        Ok(())
    }

    /// Test that proves the optimized batch_merge implementation handles filters
    /// equivalently to the default implementation.
    ///
    /// This test directly compares both implementations with filters by:
    /// 1. Running the reference implementation with a filter
    /// 2. Running the optimized implementation logic with the same filter
    /// 3. Verifying both produce identical results
    #[test]
    fn test_batch_merge_with_filter() -> Result<()> {
        let adaptor = create_mock_adaptor();

        // Create test state data with filter
        let values = vec![10u64, 20u64, 30u64, 40u64];
        let flags = vec![true, false, true, true];
        let filter_bits = vec![true, false, true, false]; // Only process indices 0 and 2

        let state_entry = create_test_state_entry(&values, &flags)?;
        let filter = Bitmap::from_iter(filter_bits.iter().copied());

        // Setup state layout - create a manual layout that matches the adaptor structure
        // The adaptor has: nested state (u64) + bool flag
        let nested_layout = std::alloc::Layout::new::<u64>();
        let bool_layout = std::alloc::Layout::new::<bool>();
        let (layout, nested_offset) = nested_layout.extend(bool_layout).unwrap();
        let layout = layout.pad_to_align();

        // Create location descriptors
        let loc = vec![
            AggrStateLoc::Custom(0, 0),           // nested state at offset 0
            AggrStateLoc::Bool(1, nested_offset), // bool flag at calculated offset
        ];

        // Create test buffers - we need separate copies for each implementation
        let mut reference_buffers: Vec<TestStateBuffer> = (0..values.len())
            .map(|_| TestStateBuffer::new(&layout))
            .collect();
        let mut optimized_buffers: Vec<TestStateBuffer> = (0..values.len())
            .map(|_| TestStateBuffer::new(&layout))
            .collect();

        // Test 1: Run reference implementation with filter
        reference_batch_merge(
            &adaptor,
            &mut reference_buffers,
            &loc,
            &layout,
            &state_entry,
            Some(&filter),
        )?;

        // Test 2: Run optimized implementation logic with filter
        optimized_batch_merge_logic(
            &adaptor,
            &mut optimized_buffers,
            &loc,
            &layout,
            &state_entry,
            Some(&filter),
        )?;

        // Test 3: Compare buffer contents
        // Only compare buffers that should have been processed (where filter=true AND flag=true)
        let processed_indices: Vec<usize> = filter_bits
            .iter()
            .enumerate()
            .filter_map(|(i, &should_process)| {
                if should_process && flags[i] {
                    Some(i)
                } else {
                    None
                }
            })
            .collect();

        for &i in &processed_indices {
            // Compare the actual buffer contents for processed items
            assert_eq!(
                reference_buffers[i].buffer, optimized_buffers[i].buffer,
                "Buffer contents mismatch at index {}",
                i
            );
        }

        // Test 4: Verify correct filtering behavior
        // Only indices where filter=true AND flag=true should be processed
        let expected_processed_indices: Vec<usize> = filter_bits
            .iter()
            .enumerate()
            .filter_map(|(i, &should_process)| {
                if should_process && flags[i] {
                    Some(i)
                } else {
                    None
                }
            })
            .collect();

        assert_eq!(
            expected_processed_indices,
            vec![0, 2],
            "Expected to process indices 0 and 2 (where filter=true and flag=true)"
        );

        assert_eq!(
            processed_indices.len(),
            expected_processed_indices.len(),
            "Both implementations should process exactly the expected indices"
        );

        Ok(())
    }

    /// Test that verifies the optimized flag processing logic is equivalent to reference logic
    #[test]
    fn test_flag_processing_equivalence() -> Result<()> {
        // Test data
        let flags = vec![true, false, true, true, false];
        let filter_bits = vec![true, true, false, true, false];
        let filter = Bitmap::from_iter(filter_bits.iter().copied());

        // Create test buffers
        let nested_layout = std::alloc::Layout::new::<u64>();
        let bool_layout = std::alloc::Layout::new::<bool>();
        let (layout, nested_offset) = nested_layout.extend(bool_layout).unwrap();
        let layout = layout.pad_to_align();

        let loc = vec![
            AggrStateLoc::Custom(0, 0),
            AggrStateLoc::Bool(1, nested_offset),
        ];

        let test_buffers: Vec<TestStateBuffer> = (0..flags.len())
            .map(|_| TestStateBuffer::new(&layout))
            .collect();

        // Test 1: Without filter
        let optimized_result = test_optimized_flag_processing(&test_buffers, &loc, &flags, None);
        let reference_result = test_reference_flag_processing(&test_buffers, &loc, &flags, None);

        assert_eq!(
            optimized_result, reference_result,
            "Flag processing should be equivalent without filter"
        );
        assert_eq!(
            optimized_result,
            vec![true, false, true, true, false],
            "Should process items where flag=true"
        );

        // Test 2: With filter
        let optimized_result =
            test_optimized_flag_processing(&test_buffers, &loc, &flags, Some(&filter));
        let reference_result =
            test_reference_flag_processing(&test_buffers, &loc, &flags, Some(&filter));

        assert_eq!(
            optimized_result, reference_result,
            "Flag processing should be equivalent with filter"
        );
        assert_eq!(
            optimized_result,
            vec![true, false, false, true, false],
            "Should process items where filter=true AND flag=true"
        );

        // Test 3: Edge cases
        let all_true_flags = vec![true; 5];
        let optimized_result =
            test_optimized_flag_processing(&test_buffers, &loc, &all_true_flags, Some(&filter));
        let reference_result =
            test_reference_flag_processing(&test_buffers, &loc, &all_true_flags, Some(&filter));

        assert_eq!(
            optimized_result, reference_result,
            "Should handle all-true flags correctly"
        );
        assert_eq!(
            optimized_result, filter_bits,
            "With all flags true, should match filter exactly"
        );

        let all_false_flags = vec![false; 5];
        let optimized_result =
            test_optimized_flag_processing(&test_buffers, &loc, &all_false_flags, Some(&filter));
        let reference_result =
            test_reference_flag_processing(&test_buffers, &loc, &all_false_flags, Some(&filter));

        assert_eq!(
            optimized_result, reference_result,
            "Should handle all-false flags correctly"
        );
        assert_eq!(
            optimized_result,
            vec![false; 5],
            "With all flags false, should process nothing"
        );

        Ok(())
    }

    /// Test that verifies the batch processing optimization maintains correctness
    #[test]
    fn test_batch_processing_optimization() -> Result<()> {
        let adaptor = create_mock_adaptor();

        // Create test data with mixed scenarios
        let values = vec![100u64, 200u64, 300u64, 400u64];
        let flags = vec![true, true, false, true]; // Skip index 2
        let filter_bits = vec![true, false, true, true]; // Skip index 1
        let filter = Bitmap::from_iter(filter_bits.iter().copied());

        let state_entry = create_test_state_entry(&values, &flags)?;

        // Setup layout
        let nested_layout = std::alloc::Layout::new::<u64>();
        let bool_layout = std::alloc::Layout::new::<bool>();
        let (layout, nested_offset) = nested_layout.extend(bool_layout).unwrap();
        let layout = layout.pad_to_align();

        let loc = vec![
            AggrStateLoc::Custom(0, 0),
            AggrStateLoc::Bool(1, nested_offset),
        ];

        // Create separate buffer sets
        let mut reference_buffers: Vec<TestStateBuffer> = (0..values.len())
            .map(|_| TestStateBuffer::new(&layout))
            .collect();
        let mut optimized_buffers: Vec<TestStateBuffer> = (0..values.len())
            .map(|_| TestStateBuffer::new(&layout))
            .collect();

        // Run both implementations
        reference_batch_merge(
            &adaptor,
            &mut reference_buffers,
            &loc,
            &layout,
            &state_entry,
            Some(&filter),
        )?;
        optimized_batch_merge_logic(
            &adaptor,
            &mut optimized_buffers,
            &loc,
            &layout,
            &state_entry,
            Some(&filter),
        )?;

        // Verify results
        // Expected processing: index 0 (filter=true, flag=true) and index 3 (filter=true, flag=true)
        // Skipped: index 1 (filter=false), index 2 (flag=false)

        for i in 0..values.len() {
            let should_be_processed = filter_bits[i] && flags[i];

            if should_be_processed {
                // Compare processed buffers
                assert_eq!(
                    reference_buffers[i].buffer, optimized_buffers[i].buffer,
                    "Processed buffer contents should match at index {}",
                    i
                );

                // Verify the state contains the expected value
                let place = AggrState::new(reference_buffers[i].addr(), &loc);
                let state_value = *place.get::<u64>();
                assert_eq!(
                    state_value, values[i],
                    "State should contain the input value at index {}",
                    i
                );

                // Verify the flag is set
                let flag_value = get_flag(place);
                assert!(
                    flag_value,
                    "Flag should be true for processed item at index {}",
                    i
                );
            } else {
                // Verify unprocessed buffers remain in initial state
                let place = AggrState::new(reference_buffers[i].addr(), &loc);
                let state_value = *place.get::<u64>();
                assert_eq!(
                    state_value, 0,
                    "Unprocessed state should remain 0 at index {}",
                    i
                );

                let flag_value = get_flag(place);
                assert!(
                    !flag_value,
                    "Flag should be false for unprocessed item at index {}",
                    i
                );
            }
        }

        Ok(())
    }

    /// Test using ConfigurableNullAggregateFunction with different strategies
    #[test]
    fn test_configurable_aggregate_function_equivalence() -> Result<()> {
        // Create test data
        let values = vec![100u64, 200u64, 300u64, 400u64];
        let flags = vec![true, false, true, true]; // Skip index 1
        let state_entry = create_test_state_entry(&values, &flags)?;

        // Create functions with different strategies
        let reference_func = create_configurable(BatchMergeStrategy::Reference);
        let optimized_func = Arc::new(create_configurable(BatchMergeStrategy::Optimized));

        // Setup state layout using the actual function's state registration
        let states_layout = get_states_layout(&[optimized_func.clone()])?;
        let layout = states_layout.layout;
        let loc = &states_layout.states_loc[0];

        // Create separate buffer sets for each function
        let reference_buffers: Vec<TestStateBuffer> = (0..values.len())
            .map(|_| TestStateBuffer::new(&layout))
            .collect();
        let optimized_buffers: Vec<TestStateBuffer> = (0..values.len())
            .map(|_| TestStateBuffer::new(&layout))
            .collect();

        // Initialize states
        for buffer in &reference_buffers {
            let place = AggrState::new(buffer.addr(), &loc);
            reference_func.init_state(place);
        }
        for buffer in &optimized_buffers {
            let place = AggrState::new(buffer.addr(), &loc);
            optimized_func.init_state(place);
        }

        // Run batch_merge with both strategies
        let places_ref: Vec<StateAddr> = reference_buffers.iter().map(|b| b.addr()).collect();
        let places_opt: Vec<StateAddr> = optimized_buffers.iter().map(|b| b.addr()).collect();

        reference_func.batch_merge(&places_ref, &loc, &state_entry, None)?;
        optimized_func.batch_merge(&places_opt, &loc, &state_entry, None)?;

        // Compare results
        for i in 0..values.len() {
            if flags[i] {
                // Compare processed buffers
                assert_eq!(
                    reference_buffers[i].buffer, optimized_buffers[i].buffer,
                    "Buffer contents should match at index {}",
                    i
                );

                // Verify the state contains the expected value
                let place = AggrState::new(reference_buffers[i].addr(), &loc);
                let state_value = *place.get::<u64>();
                assert_eq!(
                    state_value, values[i],
                    "State should contain the input value at index {}",
                    i
                );

                // Verify the flag is set
                let flag_value = get_flag(place);
                assert!(
                    flag_value,
                    "Flag should be true for processed item at index {}",
                    i
                );
            } else {
                // Verify unprocessed buffers remain in initial state
                let place = AggrState::new(reference_buffers[i].addr(), &loc);
                let state_value = *place.get::<u64>();
                assert_eq!(
                    state_value, 0,
                    "Unprocessed state should remain 0 at index {}",
                    i
                );

                let flag_value = get_flag(place);
                assert!(
                    !flag_value,
                    "Flag should be false for unprocessed item at index {}",
                    i
                );
            }
        }

        Ok(())
    }

    /// Test configurable function with filter
    #[test]
    fn test_configurable_aggregate_function_with_filter() -> Result<()> {
        // Create test data
        let values = vec![100u64, 200u64, 300u64, 400u64];
        let flags = vec![true, true, false, true]; // Skip index 2
        let filter_bits = vec![true, false, true, true]; // Skip index 1
        let filter = Bitmap::from_iter(filter_bits.iter().copied());
        let state_entry = create_test_state_entry(&values, &flags)?;

        // Create functions with different strategies
        let reference_func = Arc::new(create_configurable(BatchMergeStrategy::Reference));
        let optimized_func = create_configurable(BatchMergeStrategy::Optimized);

        // Setup state layout using the actual function's state registration
        let states_layout = get_states_layout(&[reference_func.clone()])?;
        let layout = states_layout.layout;
        let loc = &states_layout.states_loc[0];

        // Create separate buffer sets
        let reference_buffers: Vec<TestStateBuffer> = (0..values.len())
            .map(|_| TestStateBuffer::new(&layout))
            .collect();
        let optimized_buffers: Vec<TestStateBuffer> = (0..values.len())
            .map(|_| TestStateBuffer::new(&layout))
            .collect();

        // Initialize states
        for buffer in &reference_buffers {
            let place = AggrState::new(buffer.addr(), &loc);
            reference_func.init_state(place);
        }
        for buffer in &optimized_buffers {
            let place = AggrState::new(buffer.addr(), &loc);
            optimized_func.init_state(place);
        }

        // Run batch_merge with filter
        let places_ref: Vec<StateAddr> = reference_buffers.iter().map(|b| b.addr()).collect();
        let places_opt: Vec<StateAddr> = optimized_buffers.iter().map(|b| b.addr()).collect();

        reference_func.batch_merge(&places_ref, &loc, &state_entry, Some(&filter))?;
        optimized_func.batch_merge(&places_opt, &loc, &state_entry, Some(&filter))?;

        // Verify results
        // Expected processing: index 0 (filter=true, flag=true) and index 3 (filter=true, flag=true)
        // Skipped: index 1 (filter=false), index 2 (flag=false)

        for i in 0..values.len() {
            let should_be_processed = filter_bits[i] && flags[i];

            if should_be_processed {
                // Compare processed buffers
                assert_eq!(
                    reference_buffers[i].buffer, optimized_buffers[i].buffer,
                    "Processed buffer contents should match at index {}",
                    i
                );

                // Verify the state contains the expected value
                let place = AggrState::new(reference_buffers[i].addr(), &loc);
                let state_value = *place.get::<u64>();
                assert_eq!(
                    state_value, values[i],
                    "State should contain the input value at index {}",
                    i
                );

                // Verify the flag is set
                let flag_value = get_flag(place);
                assert!(
                    flag_value,
                    "Flag should be true for processed item at index {}",
                    i
                );
            } else {
                // Verify unprocessed buffers remain in initial state
                let place = AggrState::new(reference_buffers[i].addr(), &loc);
                let state_value = *place.get::<u64>();
                assert_eq!(
                    state_value, 0,
                    "Unprocessed state should remain 0 at index {}",
                    i
                );

                let flag_value = get_flag(place);
                assert!(
                    !flag_value,
                    "Flag should be false for unprocessed item at index {}",
                    i
                );
            }
        }

        // Verify expected processing pattern
        let processed_indices: Vec<usize> = (0..values.len())
            .filter(|&i| filter_bits[i] && flags[i])
            .collect();
        assert_eq!(
            processed_indices,
            vec![0, 3],
            "Should process indices 0 and 3 (where filter=true and flag=true)"
        );

        Ok(())
    }
}
